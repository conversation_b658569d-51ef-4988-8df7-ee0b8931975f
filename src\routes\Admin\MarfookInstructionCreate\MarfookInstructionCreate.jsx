import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Divider,
  Grid,
  IconButton,
  TextField,
  Typography,
} from '@mui/material';
import { useMutation } from 'react-query';
import { useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { ContentPasteGoOutlined, Add, Delete } from '@mui/icons-material';
import { createMarfookInstruction } from '../../../apis/marfook';
import { setSnackbar } from '../../../store/layout';
import JalaliDateTimePicker from '../../../components/JalaliDateTimePicker/JalaliDateTimePicker';
import dayjs from 'dayjs';
import JalaliDatePicker from 'components/SearchBox/components/JalaliDatePicker/JalaliDatePicker';

export default function MarfookInstructionCreate({ content = {} }) {
  const descriptionRef = useRef(null);
  const formRef = useRef(null);

  const [progress, setProgress] = useState(0);
  const [loading, setLoading] = useState(false);

  // State for instructions
  const [instructions, setInstructions] = useState([
    { title: '', description: '', hashtags: [] },
  ]);

  // State for expiration date
  const [expirationDate, setExpirationDate] = useState(null);

  const pasteDescription = async () => {
    descriptionRef.current.value = await navigator.clipboard.readText();
  };

  // Functions for managing instructions
  const addInstruction = () => {
    setInstructions([
      ...instructions,
      { title: '', description: '', hashtags: [] },
    ]);
  };

  const removeInstruction = index => {
    if (instructions.length > 1) {
      setInstructions(instructions.filter((_, i) => i !== index));
    }
  };

  const updateInstructionTitle = (index, title) => {
    const newInstructions = [...instructions];
    newInstructions[index].title = title;
    setInstructions(newInstructions);
  };

  const updateInstructionDescription = (index, description) => {
    const newInstructions = [...instructions];
    newInstructions[index].description = description;
    setInstructions(newInstructions);
  };

  const updateInstructionHashtags = (index, newHashtags) => {
    const newInstructions = [...instructions];
    const existingHashtags = newInstructions[index].hashtags || [];

    // Add new hashtags to existing ones, avoiding duplicates
    const combinedHashtags = [...existingHashtags];
    newHashtags.forEach(hashtag => {
      if (!combinedHashtags.includes(hashtag)) {
        combinedHashtags.push(hashtag);
      }
    });

    newInstructions[index].hashtags = combinedHashtags;
    setInstructions(newInstructions);
  };

  const handleHashtagInput = (index, value) => {
    // Split by comma and create hashtags
    const hashtagsArray = value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
      .map(tag => (tag.startsWith('#') ? tag : `#${tag}`));

    updateInstructionHashtags(index, hashtagsArray);
  };

  const handleHashtagKeyDown = (event, index) => {
    if (event.key === 'Enter') {
      event.preventDefault(); // Prevent form submission
      handleHashtagInput(index, event.target.value);
      event.target.blur(); // Remove focus to trigger onBlur as well
    }
  };

  const removeHashtag = (instructionIndex, hashtagIndex) => {
    const newInstructions = [...instructions];
    newInstructions[instructionIndex].hashtags.splice(hashtagIndex, 1);
    setInstructions(newInstructions);
  };

  const dispatch = useDispatch();
  const abortController = useRef(null);
  const mutation = useMutation(
    ({ event }) => {
      setLoading(true);

      const { title, body } = event.target;
      if (title.value === '') {
        throw { message: 'عنوان اجباری است', severity: 'error' };
      } else if (body.value === '') {
        throw { message: 'توضیحات اعلان اجباری است', severity: 'error' };
      } else if (!expirationDate) {
        throw { message: 'تاریخ انقضا اجباری است', severity: 'error' };
      } else if (instructions.some(inst => inst.title === '')) {
        throw {
          message: 'عنوان همه دستورالعمل‌ها اجباری است',
          severity: 'error',
        };
      }

      // Format data according to API template
      const requestData = {
        subject: instructions.map(inst => ({
          title: inst.title,
          description: inst.description || '',
          hashtags: inst.hashtags,
        })),
        title: title.value,
        description: body.value,
        expiration_date: expirationDate,
      };

      return createMarfookInstruction(
        requestData,
        setProgress,
        abortController.current,
      );
    },
    {
      onSuccess: async () => {
        dispatch(
          setSnackbar({
            message: 'اعلان مرفوک با موفقیت ایجاد شد',
            severity: 'success',
          }),
        );
        setProgress(0);
        setLoading(false);
        formRef.current.reset();
        setInstructions([{ title: '', description: '', hashtags: [] }]);
        setExpirationDate(null);
      },
      onError: error => {
        if (error?.response?.status === 400) {
          dispatch(
            setSnackbar({
              message: 'خطا در ایجاد اعلان مرفوک',
              severity: 'error',
            }),
          );
        } else dispatch(setSnackbar(error));

        setProgress(0);
        setLoading(false);
      },
    },
  );

  const errors = mutation.error?.response?.data || {};

  const submitForm = event => {
    abortController.current = new AbortController();
    event.preventDefault();
    mutation.mutate({ event });
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        overflowY: 'scroll',
        mt: 2,
      }}
    >
      <form onSubmit={submitForm} ref={formRef}>
        <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
          دستور العمل
        </Typography>
        <Grid container columnSpacing={2}>
          <Grid item xs={12} lg={8} sx={{ mt: 2 }}>
            <TextField
              variant="outlined"
              required
              label="عنوان"
              name="title"
              defaultValue={content.title}
              helperText={errors.title}
              error={!!errors.title}
              disabled={mutation.isLoading}
              inputProps={{ maxLength: 100 }}
              fullWidth
            />
            <TextField
              sx={{ mt: 2 }}
              label="توضیحات اعلان"
              name="body"
              variant="outlined"
              helperText={errors.body}
              multiline
              rows={4}
              fullWidth
              inputRef={descriptionRef}
              InputProps={{
                endAdornment: (
                  <IconButton edge="end" onClick={pasteDescription}>
                    <ContentPasteGoOutlined />
                  </IconButton>
                ),
              }}
            />
            <Box sx={{ mt: 2 }}>
              <JalaliDatePicker
                inputName="expiration_date"
                label="تاریخ انقضا"
                size="medium"
                setMinDateToday
                defaultValue={dayjs()}
                onChange={setExpirationDate}
                disabled={mutation.isLoading}
                time
              />
            </Box>

            <Divider sx={{ mt: 3, mb: 3 }} />

            <Typography sx={{ fontSize: 16, fontWeight: 'bold' }}>
              محور ها
            </Typography>

            {instructions.map((instruction, index) => (
              <Box
                key={index}
                sx={{
                  mt: 2,
                  p: 2,
                  border: '1px solid #e0e0e0',
                  borderRadius: 2,
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    mb: 2,
                  }}
                >
                  <Typography sx={{ fontSize: 14, fontWeight: 'bold' }}>
                    محور {index + 1}
                  </Typography>
                  {instructions.length > 1 && (
                    <IconButton
                      onClick={() => removeInstruction(index)}
                      size="small"
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  )}
                </Box>

                <TextField
                  fullWidth
                  label="عنوان محور"
                  value={instruction.title}
                  onChange={e => updateInstructionTitle(index, e.target.value)}
                  sx={{ mb: 2 }}
                  required
                />

                {/* <TextField
                  fullWidth
                  label="توضیحات محور"
                  value={instruction.description}
                  onChange={e =>
                    updateInstructionDescription(index, e.target.value)
                  }
                  sx={{ mb: 2 }}
                  multiline
                  rows={2}
                /> */}

                <TextField
                  fullWidth
                  label="هشتگ‌ها"
                  placeholder="مثال: تگ1, تگ2, تگ3"
                  onBlur={e => handleHashtagInput(index, e.target.value)}
                  onKeyDown={e => handleHashtagKeyDown(e, index)}
                  sx={{ mb: 2 }}
                />

                {instruction.hashtags.length > 0 && (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {instruction.hashtags.map((hashtag, hashtagIndex) => (
                      <Chip
                        key={hashtagIndex}
                        label={hashtag}
                        onDelete={() => removeHashtag(index, hashtagIndex)}
                        size="small"
                        color="primary"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                )}
              </Box>
            ))}

            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={addInstruction}
              sx={{ mt: 2, width: '100%' }}
            >
              افزودن دستورالعمل جدید
            </Button>

            <Button
              variant="contained"
              size="large"
              type="submit"
              sx={{ mt: 4, width: '100%' }}
              disabled={loading}
            >
              {!loading ? (
                <>ایجاد اعلان مرفوک</>
              ) : (
                <CircularProgress sx={{ color: 'white' }} size={24} />
              )}
            </Button>
          </Grid>
        </Grid>
      </form>
    </Box>
  );
}
